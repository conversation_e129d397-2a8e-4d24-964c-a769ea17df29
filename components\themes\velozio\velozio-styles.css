@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap");

/* Animation for placeholder text - matches reference more closely */
@keyframes placeholderAnimation {
  0% {
    opacity: 0;
    transform: translateY(20px);
    visibility: hidden;
  }
  1%,
  5% {
    opacity: 1;
    transform: translateY(0);
    visibility: visible;
  }
  6%,
  100% {
    opacity: 0;
    transform: translateY(-20px);
    visibility: hidden;
  }
}

/* Animasi panah bergerak */
@keyframes arrowMove {
  0% { transform: translateX(0); }
  50% { transform: translateX(3px); }
  100% { transform: translateX(0); }
}

/* CSS untuk tombol floating arrow */
.floating-arrow {
  position: absolute;
  right: 5px;
  top: 45%;
  transform: translateY(-50%);
  height: 36px;
  background-color: rgba(255, 245, 240, 0.95);
  border: 1px solid #FFDFD1;
  border-radius: 18px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  padding: 0 15px 0 12px;
  cursor: pointer;
  z-index: 10;
  transition: opacity 0.8s ease, transform 0.8s ease;
  opacity: 1;
}

.floating-arrow.hidden {
  opacity: 0;
  transform: translateY(-50%) translateX(10px);
  pointer-events: none;
}

.floating-arrow:active {
  transform: translateY(-50%) scale(0.97);
}

.floating-arrow-text {
  font-size: 12px;
  font-weight: 600;
  color: #FF5722;
  margin-right: 6px;
}

.floating-arrow-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.floating-arrow-icon svg {
  width: 15px;
  height: 15px;
  color: #FF5722;
  animation: arrowMove 1.5s infinite ease-in-out;
}

/* Kategori Styles */
.categories-container {
  display: flex;
  overflow-x: auto;
  padding-bottom: 10px;
  gap: 12px;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 80px;
  padding: 8px;
  border-radius: 8px;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
  cursor: pointer;
}

.category-item:hover {
  transform: translateY(-2px);
}

.category-icon {
  width: 32px;
  height: 32px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-name {
  font-size: 12px;
  text-align: center;
  color: #333;
  line-height: 1.2;
}

/* Desktop Categories Grid */
.desktop-categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));
  grid-template-rows: repeat(2, auto);
  grid-auto-flow: row;
  gap: 12px;
  padding: 4px 16px 12px 4px;
  width: 100%;
}

/* Gradient untuk efek fade di sisi kanan */
.categories-container-wrapper::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 50px;
  height: 100%;
  background: linear-gradient(to right, rgba(255,255,255,0), rgba(255,255,255,0.9));
  pointer-events: none;
}

/* CSS untuk expanded categories */
.expanded-categories {
  height: 0;
  overflow: hidden;
  transition: height 0.3s ease;
}

.expanded-categories.active {
  height: auto !important;
  overflow: visible;
}

.expanded-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 10px;
  padding: 5px 10px 10px 10px;
}

@media (min-width: 768px) {
  .expanded-grid {
    grid-template-columns: repeat(7, 1fr);
    gap: 15px;
  }
}

.placeholder-animate {
  animation: placeholderAnimation 45s ease-in-out infinite;
}

/* Search input styling */
.search-input::placeholder {
  color: #666;
  opacity: 1;
}

.search-input:focus::placeholder {
  opacity: 0.7;
}

/* Cart and chat icon styling */
.cart-icon,
.chat-icon {
  position: relative;
  color: transparent;
  font-size: 22px;
  cursor: pointer;
}

/* For webkit browsers to create outline effect on icons */
@supports (-webkit-text-stroke: 1px white) {
  .cart-icon {
    -webkit-text-stroke: 1.3px white;
  }

  .chat-icon {
    -webkit-text-stroke: 2px white;
  }
}

/* For non-webkit browsers fallback */
@supports not (-webkit-text-stroke: 1px white) {
  .cart-icon,
  .chat-icon {
    color: white;
    text-shadow: 0 0 1px white;
  }
}

/* Suggestions Container Styles */
.suggestions-container {
  background-color: white;
  width: 100%;
  border-radius: 3px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-sizing: border-box;
  padding: 0;
  position: relative;
}

/* Prevent body scroll when suggestions are shown */
body.suggestions-open {
  overflow: hidden;
  position: fixed;
  width: 100%;
}

/* Text suggestions */
.text-suggestions {
  padding: 10px 0px 10px 0px;
  border-bottom: 1px solid #f2f2f2;
  display: block;
}

/* Styling yang konsisten untuk suggestion-icon */
.suggestion-icon {
  margin-right: 12px;
  width: 20px;
  display: inline-flex;
  justify-content: center;
  color: #999;
  font-size: 14px;
  flex-shrink: 0;
  align-self: flex-start;
  margin-top: 1px;
}

.suggestion-icon i {
  font-size: 14px;
  color: #999;
}

/* Icon styling untuk trending */
.additional-suggestions .suggestion-icon i {
  color: #ee4d2d !important;
  font-size: 14px;
}

/* Styling untuk batas antar item sugesti */
.suggestion-item {
  padding: 12px 15px;
  cursor: pointer;
  display: flex;
  align-items: flex-start;
  border-bottom: 1px solid #f5f5f5;
  transition: background-color 0.2s ease;
}

/* Container teks untuk item sugesti */
.suggestion-text {
  display: inline-block;
  line-height: 1.4;
  word-break: break-word;
  color: #333;
  font-size: 14px;
}

.suggestion-item:last-child {
  border-bottom: 1px solid #f5f5f5;
}

.suggestion-item:hover {
  background-color: #f9f9f9;
}

/* Styling khusus untuk ikon trend */
.additional-suggestions .suggestion-icon {
  font-size: 18px !important;
  margin-bottom: 6px;
  color: #ee4d2d;
}

/* Style untuk tombol Lihat Lainnya */
.see-more-container {
  padding: 5px 0;
  text-align: center;
  transition: all 0.2s ease;
  position: relative;
  z-index: 2;
  background-color: white;
  margin-bottom: 15px;
  display: block;
}

.see-more-btn {
  background-color: transparent;
  color: #ee4d2d;
  border: none;
  padding: 10px;
  font-size: 14px;
  cursor: pointer;
  border-radius: 4px;
  font-weight: 500;
  width: 100%;
  max-width: 300px;
  margin: 0 auto;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.see-more-btn:hover {
  background-color: transparent;
}

.see-more-btn svg {
  margin-right: 8px;
  color: #ee4d2d;
  font-size: 14px;
}

.see-more-icon {
  margin-right: 8px;
  display: inline-flex;
  align-items: center;
}

.see-more-icon i {
  color: #ee4d2d;
  font-size: 16px;
}

/* Style untuk additional suggestions dengan animasi slide down */
.additional-suggestions {
  max-height: none;
  overflow: visible;
  transition: max-height 0.3s ease;
  border-bottom: 0px solid #f2f2f2;
  background-color: white;
  position: relative;
  z-index: 1;
  margin-bottom: 15px;
  display: block;
}

.additional-suggestions.open {
  max-height: none;
  border-bottom: 1px solid #f2f2f2;
  padding-bottom: 20px;
}

.additional-suggestions .suggestion-item {
  padding: 10px 15px;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.additional-suggestions .suggestion-item:hover {
  background-color: #f9f9f9;
}

/* Styling untuk "Hapus riwayat pencarian" */
.clear-history {
  text-align: center;
  padding: 10px;
  margin-bottom: 5px;
  cursor: pointer;
  color: #999;
  font-size: 13px;
  border-bottom: 1px solid #f2f2f2;
  transition: color 0.2s ease;
}

.clear-history:hover {
  color: #ee4d2d;
}

/* Styling untuk "Sedang Trend" */
.trending-title {
  margin-top: 15px;
  color: #333;
  font-weight: 500;
  padding: 12px 15px 5px;
  font-size: 13px;
  display: flex;
  align-items: center;
}

.trending-title .trend-icon {
  margin-left: 5px;
  font-size: 13px;
  color: #ee4d2d;
}

/* Product suggestions */
.product-suggestions {
  padding: 10px;
  width: 100%;
  box-sizing: border-box;
  margin-top: 10px;
}

.product-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
  padding: 0 5px;
  display: block;
}

/* Grid produk standar */
.product-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  width: 100%;
  box-sizing: border-box;
  border: 2px solid #eee;
  border-radius: 5px;
  margin-top: 30px;
  padding: 10px 10px 25px 10px;
  transform: translateY(0px);
}

/* Card produk simpel (tampilan pencarian awal) */
.simple-product-card {
  background-color: white;
  border-radius: 3px;
  transform: translateY(10px);
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  text-align: center;
}

.simple-product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.product-img {
  width: 100%;
  height: 150px;
  object-fit: cover;
  display: block;
}

/* Nama produk untuk tampilan simpel */
.simple-product-name {
  font-size: 13px;
  color: #333;
  padding: 10px 5px;
  text-align: center;
  height: auto;
  max-height: 40px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.product-price {
  font-size: 14px;
  color: #ee4d2d;
  font-weight: bold;
  padding: 0 5px 10px 5px;
}

/* Keyword Predictions Container Styles */
.keyword-predictions-container {
  background-color: white;
  width: 100%;
  border-radius: 3px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-sizing: border-box;
  padding: 0;
  position: relative;
}

.prediction-item {
  padding: 12px 15px;
  cursor: pointer;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f5f5f5;
  transition: background-color 0.2s ease;
}

.prediction-item:hover {
  background-color: #f9f9f9;
}

.prediction-item:last-child {
  border-bottom: none;
}

.prediction-icon {
  margin-right: 12px;
  width: 20px;
  display: inline-flex;
  justify-content: center;
  color: #999;
  font-size: 14px;
  flex-shrink: 0;
  align-self: flex-start;
  margin-top: 1px;
}

.prediction-icon i {
  font-size: 14px;
  color: #999;
}

.prediction-text {
  display: inline-block;
  line-height: 1.4;
  word-break: break-word;
  color: #333;
  font-size: 14px;
}

/* Responsive styles */
@media (min-width: 768px) {
  .product-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
  }

  .suggestions-container {
    max-width: 800px;
    position: relative;
  }

  .keyword-predictions-container {
    max-width: 800px;
    position: relative;
  }

  .product-img {
    height: 180px;
  }

  .simple-product-name {
    font-size: 14px;
  }
}

@media (min-width: 1024px) {
  .product-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
  }

  .product-img {
    height: 200px;
  }
}
