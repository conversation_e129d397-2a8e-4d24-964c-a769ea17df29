"use client"

import { useState, useRef, useEffect } from "react"
import { Search, X, ArrowLeft, Clock, TrendingUp, ChevronDown, Plus, Minus } from "lucide-react"
import { VelozioFilterTabs } from "./velozio-filter-tabs"
import "./velozio-styles.css"

export function VelozioHeader() {
  const [isExpanded, setIsExpanded] = useState(false)
  const [searchText, setSearchText] = useState("")
  const [focused, setFocused] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [showMoreSuggestions, setShowMoreSuggestions] = useState(false)
  const [showFilterTabs, setShowFilterTabs] = useState(false)
  const [isSearchActive, setIsSearchActive] = useState(false)
  const [showKeywordPredictions, setShowKeywordPredictions] = useState(false)
  const searchInputRef = useRef<HTMLInputElement>(null)
  const expandedSearchInputRef = useRef<HTMLInputElement>(null)
  
  // Simulasi loading state
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 800) // Menunggu 800ms untuk simulasi loading

    return () => clearTimeout(timer)
  }, [])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Restore body scroll on unmount
      document.body.classList.remove('suggestions-open')
    }
  }, [])

  // Placeholder texts for animation
  const placeholderTexts = [
    "Handphone Samsung",
    "Sepatu Pria",
    "Tas Wanita",
    "Promo Elektronik",
    "Laptop Gaming",
    "Kamera Mirrorless",
    "Smart TV Android",
    "Headphone Bluetooth",
  ]

  // Data untuk suggestions
  const searchHistory = [
    "Smartphone Android",
    "Sepatu Sneakers",
    "Tas Selempang",
    "Headphone Bluetooth",
    "Keyboard Gaming",
  ]

  const trendingSuggestions = [
    "iPhone 15 Pro Max",
    "Samsung Galaxy S24",
    "MacBook Air M3",
    "AirPods Pro 2",
    "iPad Air 5",
    "Apple Watch Series 9",
    "Sony WH-1000XM5",
    "Nintendo Switch OLED",
  ]

  const additionalSuggestions = [
    "Laptop ASUS ROG",
    "Kamera Canon EOS",
    "Drone DJI Mini",
    "Smartwatch Garmin",
    "Speaker JBL",
    "Mouse Logitech",
    "Monitor Gaming",
    "SSD External",
  ]

  // Sample products untuk suggestions
  const sampleProducts = [
    {
      id: 1,
      name: "iPhone 15 Pro Max 256GB",
      price: "Rp 20.999.000",
      image: "https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=300&h=300&fit=crop",
    },
    {
      id: 2,
      name: "Samsung Galaxy S24 Ultra",
      price: "Rp 18.999.000",
      image: "https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=300&h=300&fit=crop",
    },
    {
      id: 3,
      name: "MacBook Air M3 13 inch",
      price: "Rp 16.999.000",
      image: "https://images.unsplash.com/photo-1517336714731-489689fd1ca8?w=300&h=300&fit=crop",
    },
    {
      id: 4,
      name: "AirPods Pro 2nd Gen",
      price: "Rp 3.999.000",
      image: "https://images.unsplash.com/photo-1606220945770-b5b6c2c55bf1?w=300&h=300&fit=crop",
    },
  ]

  // Keyword predictions data
  const keywordPredictions = [
    { text: "tas wanita", type: "history" },
    { text: "tas wanita kulit", type: "search" },
    { text: "tas wanita branded", type: "search" },
    { text: "tas wanita murah", type: "search" },
    { text: "tas selempang", type: "trending" },
    { text: "tas ransel", type: "trending" },
    { text: "sepatu pria", type: "history" },
    { text: "sepatu sneakers", type: "search" },
    { text: "sepatu formal", type: "search" },
    { text: "handphone samsung", type: "trending" },
    { text: "laptop gaming", type: "product" },
    { text: "kamera canon", type: "product" },
  ]

  const handleSearchClick = () => {
    setIsExpanded(true)
    setShowSuggestions(true)
    // Focus on the expanded search input after a short delay
    setTimeout(() => {
      if (expandedSearchInputRef.current) {
        expandedSearchInputRef.current.focus()
      }
    }, 100)
  }

  const handleBackClick = () => {
    setIsExpanded(false)
    setSearchText("")
    setShowSuggestions(false)
    setShowMoreSuggestions(false)
    setShowFilterTabs(false)
    setIsSearchActive(false)
    // Restore body scroll
    document.body.classList.remove('suggestions-open')
  }

  const handleClearClick = () => {
    setSearchText("")
    setShowSuggestions(true)
    setShowKeywordPredictions(false)
    setShowFilterTabs(false)
    setIsSearchActive(false)
    setShowMoreSuggestions(false)
    // Restore body scroll
    document.body.classList.remove('suggestions-open')
    if (expandedSearchInputRef.current) {
      expandedSearchInputRef.current.focus()
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setSearchText(value)

    // Perilaku berdasarkan panjang input sesuai facet.html
    if (value.length === 0) {
      // Input kosong - tampilkan suggestions normal, sembunyikan predictions
      setShowSuggestions(true)
      setShowKeywordPredictions(false)
      setShowFilterTabs(false)
      setIsSearchActive(false)
      // Restore body scroll
      document.body.classList.remove('suggestions-open')
    } else if (value.length >= 1) {
      // Mulai mengetik - tampilkan keyword predictions, sembunyikan suggestions
      setShowSuggestions(false)
      setShowKeywordPredictions(true)
      setShowMoreSuggestions(false)
    }
  }

  const handleSuggestionClick = (suggestion: string) => {
    setSearchText(suggestion)
    setShowSuggestions(false)
    setShowMoreSuggestions(false)
    setIsSearchActive(true)
    setShowFilterTabs(true)
    // Restore body scroll
    document.body.classList.remove('suggestions-open')
    // Here you would typically trigger a search
    console.log("Searching for:", suggestion)
  }

  const handleSearch = () => {
    if (searchText.trim()) {
      setIsSearchActive(true)
      setShowFilterTabs(true)
      setShowSuggestions(false)
      setShowMoreSuggestions(false)
      // Restore body scroll
      document.body.classList.remove('suggestions-open')
      console.log("Executing search for:", searchText)
    }
  }

  const handleSeeMoreClick = () => {
    setShowMoreSuggestions(!showMoreSuggestions)
  }

  const handleClearHistory = () => {
    // Here you would clear the search history
    console.log("Clearing search history")
  }

  const handleFocus = () => {
    setFocused(true)
    if (searchText.length === 0) {
      setShowSuggestions(true)
      // Prevent body scroll when suggestions are shown
      document.body.classList.add('suggestions-open')
    }
  }

  const handleBlur = () => {
    setFocused(false)
    // Delay hiding suggestions to allow clicking on them
    setTimeout(() => {
      setShowSuggestions(false)
      setShowMoreSuggestions(false)
      // Restore body scroll
      document.body.classList.remove('suggestions-open')
    }, 200)
  }

  // Filter suggestions based on search text
  const getFilteredSuggestions = () => {
    if (searchText.length === 0) {
      return searchHistory.slice(0, 5)
    }

    const filtered = [...searchHistory, ...trendingSuggestions, ...additionalSuggestions]
      .filter(item => item.toLowerCase().includes(searchText.toLowerCase()))
      .slice(0, 8)

    return filtered
  }

  // Get filtered keyword predictions
  const getFilteredPredictions = () => {
    if (searchText.length === 0) {
      return []
    }

    return keywordPredictions
      .filter(item => item.text.toLowerCase().includes(searchText.toLowerCase()))
      .slice(0, 8)
  }

  // Get icon for prediction type
  const getPredictionIcon = (type: string) => {
    switch (type) {
      case 'history':
        return 'fa-history'
      case 'trending':
        return 'fa-arrow-trend-up'
      case 'product':
        return 'fa-shopping-cart'
      default:
        return 'fa-search'
    }
  }

  return (
    <>
      <style jsx global>{`
        @keyframes placeholderAnimation {
          0% {
            opacity: 0;
            transform: translateY(20px);
            visibility: hidden;
          }
          1%, 5% {
            opacity: 1;
            transform: translateY(0);
            visibility: visible;
          }
          6%, 100% {
            opacity: 0;
            transform: translateY(-20px);
            visibility: hidden;
          }
        }

        .search-placeholder {
          position: absolute;
          left: 15px;
          top: 50%;
          transform: translateY(-50%);
          color: #ee4d2d;
          pointer-events: none;
          font-size: 14px;
          transition: opacity 0.3s ease-in-out;
          display: flex !important;
          flex-direction: column;
          width: calc(100% - 60px);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          height: 20px;
          z-index: 11;
          opacity: 0; /* Start hidden */
          animation: fadeIn 0.5s ease-in-out 0.3s forwards; /* Fade in after page loads */
        }
        
        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }

        .placeholder-dynamic {
          position: relative;
          width: 100%;
          overflow: hidden;
          height: 20px;
          max-width: calc(100% - 60px);
          line-height: 1.2;
          opacity: 1;
          transition: opacity 0.3s ease-in-out;
        }

        .placeholder-text {
          position: absolute;
          width: 100%;
          display: flex;
          align-items: center;
          opacity: 0;
          visibility: hidden;
          top: 0;
          left: 0;
          animation: placeholderAnimation 45s infinite; /* 45 detik total untuk 15 item */
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          will-change: transform, opacity; /* Optimize for animation performance */
        }

        /* Delay animation untuk setiap placeholder text */
        .placeholder-text:nth-child(1) { animation-delay: 0s; }
        .placeholder-text:nth-child(2) { animation-delay: 3s; }
        .placeholder-text:nth-child(3) { animation-delay: 6s; }
        .placeholder-text:nth-child(4) { animation-delay: 9s; }
        .placeholder-text:nth-child(5) { animation-delay: 12s; }
        .placeholder-text:nth-child(6) { animation-delay: 15s; }
        .placeholder-text:nth-child(7) { animation-delay: 18s; }
        .placeholder-text:nth-child(8) { animation-delay: 21s; }
        
        /* Skeleton loading animation */
        @keyframes shimmer {
          0% { background-position: -468px 0; }
          100% { background-position: 468px 0; }
        }
      `}</style>

      {/* Main header - shown when not expanded */}
      {!isExpanded && (
        <header className="fixed top-0 left-0 w-full bg-[#ee4d2d] py-3 px-4 z-50">
          <div className="w-full max-w-[800px] mx-auto relative">
            <div
              className="flex items-center relative rounded-lg border-2 border-[#ee4d2d] shadow-sm"
              onClick={handleSearchClick}
              style={{
                backgroundColor: "#FFFFFF",
                width: "calc(100% - 80px)" /* Mengurangi lebar untuk memberikan ruang bagi ikon */,
                position: "relative",
              }}
            >
              <input
                type="text"
                className="w-[calc(100%-30px)] py-2 px-4 pr-10 rounded-lg text-sm outline-none bg-white text-gray-800 search-input"
                placeholder=" "
                readOnly
                ref={searchInputRef}
                style={{
                  backgroundColor: "#FFFFFF",
                  color: "#333333",
                }}
                id="searchInput"
              />

              {/* Skeleton loading atau animated placeholder */}
              {isLoading ? (
                <div className="search-placeholder" style={{
                  background: 'linear-gradient(to right, #f6f7f8 0%, #edeef1 20%, #f6f7f8 40%, #f6f7f8 100%)',
                  backgroundSize: '800px 104px',
                  animation: 'shimmer 1.5s infinite linear',
                  width: '70%',
                  height: '14px',
                  borderRadius: '4px'
                }}></div>
              ) : (
                <div className="search-placeholder" id="searchPlaceholder">
                  <div className="placeholder-dynamic">
                    {placeholderTexts.map((text, index) => (
                      <div key={index} className="placeholder-text">
                        {text}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="absolute right-3 top-1/2 -translate-y-1/2 text-[#ee4d2d]">
                <Search size={18} strokeWidth={2} />
              </div>
            </div>

            {/* Cart and Chat icons - USING SVG DIRECTLY */}
            <div className="absolute top-1/2 -translate-y-1/2 flex items-center z-10 pt-[5px]" style={{ right: "8px" }}>
              {/* Cart icon with badge */}
              <div className="relative">
                {/* Custom SVG for shopping cart */}
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="22"
                  height="22"
                  viewBox="0 0 576 512"
                  className="cursor-pointer"
                >
                  <path
                    fill="#ee4d2d"
                    stroke="white"
                    strokeWidth="30"
                    d="M0 24C0 10.7 10.7 0 24 0H69.5c22 0 41.5 12.8 50.6 32h411c26.3 0 45.5 25 38.6 50.4l-41 152.3c-8.5 31.4-37 53.3-69.5 53.3H170.7l5.4 28.5c2.2 11.3 12.1 19.5 23.6 19.5H488c13.3 0 24 10.7 24 24s-10.7 24-24 24H199.7c-34.6 0-64.3-24.6-70.7-58.5L77.4 54.5c-.7-3.8-4-6.5-7.9-6.5H24C10.7 48 0 37.3 0 24zM128 464a48 48 0 1 1 96 0 48 48 0 1 1 -96 0zm336-48a48 48 0 1 1 0 96 48 48 0 1 1 0-96z"
                  />
                </svg>

                {/* Badge in front of icon - moved slightly upward */}
                <div className="absolute -top-3 -right-2 bg-white text-[#ee4d2d] text-[11px] font-bold rounded-full w-[18px] h-[18px] flex items-center justify-center z-20">
                  5
                </div>
              </div>

              {/* Chat icon with badge - reduced margin */}
              <div className="relative ml-[15px]">
                {/* Custom SVG for comment with custom dots */}
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="22"
                  height="22"
                  viewBox="0 0 512 512"
                  className="cursor-pointer"
                >
                  <path
                    fill="#ee4d2d"
                    stroke="white"
                    strokeWidth="30"
                    d="M256 448c141.4 0 256-93.1 256-208S397.4 32 256 32S0 125.1 0 240c0 45.1 17.7 86.8 47.7 120.9c-1.9 24.5-11.4 46.3-21.4 62.9c-5.5 9.2-11.1 16.6-15.2 21.6c-2.1 2.5-3.7 4.4-4.9 5.7c-.6 .6-1 1.1-1.3 1.4l-.3 .3 0 0 0 0 0 0 0 0c-4.6 4.6-5.9 11.4-3.4 17.4c2.5 6 8.3 9.9 14.8 9.9c28.7 0 57.6-8.9 81.6-19.3c22.9-10 42.4-21.9 54.3-30.6c31.8 11.5 67 17.9 104.1 17.9z"
                  />
                </svg>

                {/* Custom dots (thinner) */}
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[60%] h-[30%] flex justify-between items-center z-10">
                  <div className="w-[2px] h-[2px] bg-white rounded-full"></div>
                  <div className="w-[2px] h-[2px] bg-white rounded-full"></div>
                  <div className="w-[2px] h-[2px] bg-white rounded-full"></div>
                </div>

                {/* Badge in front of icon - moved slightly upward */}
                <div className="absolute -top-3 -right-2 bg-white text-[#ee4d2d] text-[11px] font-bold rounded-full w-[18px] h-[18px] flex items-center justify-center z-20">
                  3
                </div>
              </div>
            </div>
          </div>
        </header>
      )}

      {/* Expanded search header */}
      {isExpanded && (
        <>
          <div className="fixed top-0 left-0 w-full bg-white py-3 px-4 z-50 shadow-md">
            <div className="max-w-[800px] mx-auto flex items-center">
              <button className="text-[#ee4d2d] mr-3" onClick={handleBackClick}>
                <ArrowLeft size={20} className="transform scale-x-[1.3]" />
              </button>

              <div className="relative flex-1">
                <input
                  type="text"
                  className="w-full py-2 px-4 pr-10 rounded-lg text-sm border-2 border-[#ee4d2d] outline-none bg-white text-gray-800 search-input"
                  placeholder=" "
                  value={searchText}
                  onChange={handleInputChange}
                  ref={expandedSearchInputRef}
                  onFocus={handleFocus}
                  onBlur={handleBlur}
                  style={{ backgroundColor: "#FFFFFF", color: "#333333" }}
                />

                {/* Animated placeholder for expanded search */}
                {!focused && searchText === "" && !isLoading && (
                  <div className="search-placeholder">
                    <div className="placeholder-dynamic">
                      {placeholderTexts.map((text, index) => (
                        <div key={index} className="placeholder-text">
                          {text}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                
                {/* Skeleton loading untuk expanded search */}
                {!focused && searchText === "" && isLoading && (
                  <div className="search-placeholder" style={{
                    background: 'linear-gradient(to right, #f6f7f8 0%, #edeef1 20%, #f6f7f8 40%, #f6f7f8 100%)',
                    backgroundSize: '800px 104px',
                    animation: 'shimmer 1.5s infinite linear',
                    width: '70%',
                    height: '14px',
                    borderRadius: '4px'
                  }}></div>
                )}

                {/* Clear button - only show when there's text */}
                {searchText && (
                  <button
                    className="absolute right-12 top-1/2 -translate-y-1/2 bg-gray-400 rounded-full w-5 h-5 flex items-center justify-center"
                    onClick={handleClearClick}
                  >
                    <X size={12} className="text-white" />
                  </button>
                )}
              </div>

              {/* Search button */}
              <button className="ml-3 bg-[#ee4d2d] text-white p-2 rounded-lg" onClick={handleSearch}>
                <Search size={20} />
              </button>
            </div>
          </div>

          {/* Suggestions Container */}
          {showSuggestions && (
            <div className="fixed top-[69px] left-0 right-0 bg-white shadow-lg z-40 w-full">
              <div className="suggestions-container max-w-[800px] mx-auto">
                {/* Search History / Text Suggestions */}
                {searchText.length === 0 ? (
                  <>
                    {/* Clear History Button */}
                    <div className="clear-history" onClick={handleClearHistory}>
                      Hapus riwayat pencarian
                    </div>

                    {/* Search History */}
                    <div className="text-suggestions">
                      {searchHistory.map((item, index) => (
                        <div
                          key={index}
                          className="suggestion-item"
                          onClick={() => handleSuggestionClick(item)}
                        >
                          <div className="suggestion-icon">
                            <i className="fa fa-history"></i>
                          </div>
                          <span className="suggestion-text">{item}</span>
                        </div>
                      ))}
                    </div>

                    {/* See More Button */}
                    <div className="see-more-container">
                      <button className="see-more-btn" onClick={handleSeeMoreClick}>
                        <div className="see-more-icon">
                          <i className={`fa ${showMoreSuggestions ? 'fa-minus-circle' : 'fa-plus-circle'}`}></i>
                        </div>
                        Lihat Lainnya
                      </button>
                    </div>

                    {/* Additional Suggestions */}
                    {showMoreSuggestions && (
                      <div className="additional-suggestions open">
                        {additionalSuggestions.map((item, index) => (
                          <div
                            key={index}
                            className="suggestion-item"
                            onClick={() => handleSuggestionClick(item)}
                          >
                            <div className="suggestion-icon">
                              <i className="fa fa-history"></i>
                            </div>
                            <span className="suggestion-text">{item}</span>
                          </div>
                        ))}
                      </div>
                    )}

                    {/* Trending Section */}
                    <div className="trending-title">
                      Sedang Trend
                      <TrendingUp className="trend-icon" size={16} />
                    </div>
                    <div className="additional-suggestions">
                      {trendingSuggestions.slice(0, 4).map((item, index) => (
                        <div
                          key={index}
                          className="suggestion-item"
                          onClick={() => handleSuggestionClick(item)}
                        >
                          <div className="suggestion-icon">
                            <i className="fa fa-arrow-trend-up"></i>
                          </div>
                          <span className="suggestion-text">{item}</span>
                        </div>
                      ))}
                    </div>
                  </>
                ) : (
                  /* Filtered Suggestions when typing */
                  <div className="text-suggestions">
                    {getFilteredSuggestions().map((item, index) => (
                      <div
                        key={index}
                        className="suggestion-item"
                        onClick={() => handleSuggestionClick(item)}
                      >
                        <div className="suggestion-icon">
                          <i className="fa fa-search"></i>
                        </div>
                        <span className="suggestion-text">{item}</span>
                      </div>
                    ))}
                  </div>
                )}

                {/* Product Suggestions */}
                <div className="product-suggestions">
                  <div className="product-title">Produk Terkait</div>
                  <div className="product-grid">
                    {sampleProducts.map((product) => (
                      <div key={product.id} className="simple-product-card">
                        <img src={product.image} alt={product.name} className="product-img" />
                        <div className="simple-product-name">{product.name}</div>
                        <div className="product-price">{product.price}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Keyword Predictions Container */}
          {showKeywordPredictions && (
            <div className="fixed top-[69px] left-0 right-0 bg-white shadow-lg z-40 w-full">
              <div className="keyword-predictions-container max-w-[800px] mx-auto">
                {getFilteredPredictions().map((prediction, index) => (
                  <div
                    key={index}
                    className="prediction-item"
                    onClick={() => handleSuggestionClick(prediction.text)}
                  >
                    <div className="prediction-icon">
                      <i className={`fa ${getPredictionIcon(prediction.type)}`}></i>
                    </div>
                    <span className="prediction-text">{prediction.text}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Filter tabs - shown when search is active */}
          {showFilterTabs && <VelozioFilterTabs className="fixed top-[69px] left-0 z-50" />}

          {/* Additional spacer for filter tabs */}
          <div className="h-[40px]"></div>
        </>
      )}

      {/* Spacer to push content below the fixed header */}
      <div className="h-[60px]"></div>
    </>
  )
}
